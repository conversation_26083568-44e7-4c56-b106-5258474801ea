import Foundation
import Supabase
import AuthenticationServices

struct UserProfile: Codable {
    let id: String
    let email: String?
    let firstName: String?
    let lastName: String?
    let appleUserID: String?
    let birthday: Date?
    let phone: String?
    let phoneVerified: Bool
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case firstName = "first_name"
        case lastName = "last_name"
        case appleUserID = "apple_user_id"
        case birthday
        case phone
        case phoneVerified = "phone_verified"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    var displayName: String {
        if let firstName = firstName, let lastName = lastName, !firstName.isEmpty, !lastName.isEmpty {
            return "\(firstName) \(lastName)"
        } else if let firstName = firstName, !firstName.isEmpty {
            return firstName
        } else if let email = email {
            return email
        } else {
            return "User"
        }
    }
}

@MainActor
class SupabaseManager: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var userProfile: UserProfile?
    @Published var needsBirthdaySetup = false
    @Published var needsPhoneVerification = false

    private let supabase: SupabaseClient
    
    init() {
        // Local Supabase configuration - using local IP address for iOS Simulator compatibility
        let supabaseURL = URL(string: "http://**********:54321")!
        let supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
        
        self.supabase = SupabaseClient(
            supabaseURL: supabaseURL,
            supabaseKey: supabaseKey
        )
        
        // Listen for auth state changes
        Task {
            for await state in supabase.auth.authStateChanges {
                if state.event == .signedIn {
                    self.currentUser = state.session?.user
                    self.isAuthenticated = true
                    // Load user profile when signed in
                    if let user = state.session?.user {
                        await loadUserProfile(userId: user.id)
                    }
                } else if state.event == .signedOut {
                    self.currentUser = nil
                    self.userProfile = nil
                    self.isAuthenticated = false
                }
            }
        }
    }
    
    func checkExistingSession() {
        Task {
            isLoading = true
            do {
                let session = try await supabase.auth.session
                self.currentUser = session.user
                self.isAuthenticated = true
                // Load user profile for existing session
                await loadUserProfile(userId: session.user.id)
            } catch {
                print("No existing session: \(error)")
                self.isAuthenticated = false
                self.currentUser = nil
                self.userProfile = nil
            }
            isLoading = false
        }
    }
    
    // Mock sign in for testing (bypasses Apple Sign In)
    func signInWithMockUser() async throws {
        // Create a mock user for testing - simplified approach
        let mockUser = User(
            id: UUID(uuidString: "12345678-1234-1234-1234-123456789012")!,
            appMetadata: [:],
            userMetadata: [:],
            aud: "authenticated",
            confirmationSentAt: nil,
            recoverySentAt: nil,
            emailChangeSentAt: nil,
            newEmail: nil,
            invitedAt: nil,
            actionLink: nil,
            email: "<EMAIL>",
            phone: nil,
            createdAt: Date(),
            confirmedAt: Date(),
            emailConfirmedAt: Date(),
            phoneConfirmedAt: nil,
            lastSignInAt: Date(),
            role: "authenticated",
            updatedAt: Date(),
            identities: nil,
            isAnonymous: false,
            factors: nil
        )

        // Create mock profile (without birthday and phone initially)
        let mockProfile = UserProfile(
            id: mockUser.id.uuidString,
            email: "<EMAIL>",
            firstName: "Amy",
            lastName: "Wang",
            appleUserID: nil,
            birthday: nil, // No birthday set initially
            phone: nil, // No phone set initially
            phoneVerified: false,
            createdAt: Date(),
            updatedAt: Date()
        )

        // Set the mock user and profile
        self.currentUser = mockUser
        self.userProfile = mockProfile
        self.isAuthenticated = true
        self.needsBirthdaySetup = true // User needs to set birthday
        self.needsPhoneVerification = false // Will be set after birthday
        self.isLoading = false
    }

    func signInWithApple(credential: ASAuthorizationAppleIDCredential) async throws {
        guard let identityToken = credential.identityToken,
              let identityTokenString = String(data: identityToken, encoding: .utf8) else {
            throw AuthError.invalidCredentials
        }

        // Extract user information
        let email = credential.email
        let fullName = credential.fullName
        let firstName = fullName?.givenName
        let lastName = fullName?.familyName
        let appleUserID = credential.user

        do {
            // Sign in with Supabase using Apple ID token
            let session = try await supabase.auth.signInWithIdToken(
                credentials: .init(
                    provider: .apple,
                    idToken: identityTokenString
                )
            )

            // Update user profile if this is a new user and we have additional info
            // Note: Apple only provides email and name on first sign-in
            if let email = email, let firstName = firstName {
                try await updateUserProfile(
                    userId: session.user.id,
                    email: email,
                    firstName: firstName,
                    lastName: lastName,
                    appleUserID: appleUserID
                )
            } else {
                // For returning users, just update the Apple User ID if needed
                try await updateAppleUserID(
                    userId: session.user.id,
                    appleUserID: appleUserID
                )
            }

            self.currentUser = session.user
            self.isAuthenticated = true

        } catch {
            print("Supabase sign in error: \(error)")
            throw AuthError.networkError
        }
    }
    
    private func updateUserProfile(
        userId: UUID,
        email: String,
        firstName: String,
        lastName: String?,
        appleUserID: String
    ) async throws {
        let profileData: [String: AnyJSON] = [
            "id": try AnyJSON(userId.uuidString),
            "email": try AnyJSON(email),
            "first_name": try AnyJSON(firstName),
            "last_name": try AnyJSON(lastName ?? ""),
            "apple_user_id": try AnyJSON(appleUserID),
            "updated_at": try AnyJSON(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("profiles")
            .upsert(profileData)
            .execute()
    }

    private func updateAppleUserID(
        userId: UUID,
        appleUserID: String
    ) async throws {
        let updateData: [String: AnyJSON] = [
            "apple_user_id": AnyJSON("<EMAIL>"),
            "updated_at": try AnyJSON(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("profiles")
            .update(updateData)
            .eq("id", value: userId.uuidString)
            .execute()
    }
    
    private func loadUserProfile(userId: UUID) async {
        do {
            let response: [UserProfile] = try await supabase
                .from("profiles")
                .select()
                .eq("id", value: userId.uuidString)
                .execute()
                .value

            if let profile = response.first {
                self.userProfile = profile
            }
        } catch {
            print("Failed to load user profile: \(error)")
        }
    }

    func updateBirthday(_ birthday: Date) async throws {
        guard let userId = currentUser?.id else {
            throw AuthError.invalidCredentials
        }

        let dateFormatter = ISO8601DateFormatter()
        let updateData: [String: AnyJSON] = [
            "birthday": try AnyJSON(dateFormatter.string(from: birthday)),
            "updated_at": try AnyJSON(dateFormatter.string(from: Date()))
        ]

        try await supabase
            .from("profiles")
            .update(updateData)
            .eq("id", value: userId.uuidString)
            .execute()

        // Update local profile
        if let profile = userProfile {
            let updatedProfile = UserProfile(
                id: profile.id,
                email: profile.email,
                firstName: profile.firstName,
                lastName: profile.lastName,
                appleUserID: profile.appleUserID,
                birthday: birthday,
                phone: profile.phone,
                phoneVerified: profile.phoneVerified,
                createdAt: profile.createdAt,
                updatedAt: Date()
            )
            self.userProfile = updatedProfile
        }
    }

    func completeBirthdaySetup() {
        needsBirthdaySetup = false
        // Check if phone verification is needed
        if userProfile?.phone == nil || userProfile?.phoneVerified == false {
            needsPhoneVerification = true
        }
    }

    func sendPhoneVerification(phoneNumber: String) async throws {
        // For mock testing, we'll simulate sending SMS
        // In production, this would use Supabase Auth phone verification
        print("Mock: Sending SMS verification to \(phoneNumber)")

        // Store the phone number temporarily
        if let profile = userProfile {
            let updatedProfile = UserProfile(
                id: profile.id,
                email: profile.email,
                firstName: profile.firstName,
                lastName: profile.lastName,
                appleUserID: profile.appleUserID,
                birthday: profile.birthday,
                phone: phoneNumber,
                phoneVerified: false,
                createdAt: profile.createdAt,
                updatedAt: Date()
            )
            self.userProfile = updatedProfile
        }
    }

    func verifyPhoneCode(_ code: String) async throws -> Bool {
        // For mock testing, accept "1234" as valid code
        // In production, this would verify with Supabase Auth
        guard code == "1234" else {
            throw AuthError.invalidCredentials
        }

        guard let userId = currentUser?.id,
              let phoneNumber = userProfile?.phone else {
            throw AuthError.invalidCredentials
        }

        // Update phone verification in database
        let updateData: [String: AnyJSON] = [
            "phone": try AnyJSON(phoneNumber),
            "phone_verified": AnyJSON.bool(true),
            "updated_at": try AnyJSON(ISO8601DateFormatter().string(from: Date()))
        ]

        try await supabase
            .from("profiles")
            .update(updateData)
            .eq("id", value: userId.uuidString)
            .execute()

        // Update local profile
        if let profile = userProfile {
            let updatedProfile = UserProfile(
                id: profile.id,
                email: profile.email,
                firstName: profile.firstName,
                lastName: profile.lastName,
                appleUserID: profile.appleUserID,
                birthday: profile.birthday,
                phone: phoneNumber,
                phoneVerified: true,
                createdAt: profile.createdAt,
                updatedAt: Date()
            )
            self.userProfile = updatedProfile
        }

        needsPhoneVerification = false
        return true
    }

    func signOut() async {
        isLoading = true
        do {
            try await supabase.auth.signOut()
            self.currentUser = nil
            self.userProfile = nil
            self.isAuthenticated = false
            self.needsBirthdaySetup = false
            self.needsPhoneVerification = false
        } catch {
            print("Sign out error: \(error)")
        }
        isLoading = false
    }
}

enum AuthError: LocalizedError {
    case invalidCredentials
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "Invalid credentials provided"
        case .networkError:
            return "Network connection error"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}
